package com.ebon.energy.fms.mapper.third;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.AddressesDO;
import com.ebon.energy.fms.domain.entity.SiteDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface AddressesMapper extends BaseMapper<AddressesDO> {

    @Select("SELECT * FROM Addresses addr inner join Site site on addr.id=site.AddressId " +
            "inner join RedbackProductInstallations installation on installation.SiteId = site.Id and installation.InstallationEndDate is null " +
            "WHERE installation.RedbackProductSn = #{serialNumber, jdbcType=VARCHAR}")
    AddressesDO selectSiteAddressBySn(@Param("serialNumber") String serialNumber);
    
}