package com.ebon.energy.fms.domain.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RoleModifyPO {

    @NotNull
    private Integer roleId;
    @NotNull
    private String roleName;
    @NotNull
    private Boolean status;

    /**
     * 数据权限类型 0-All 1-Owner 2-Group 3-Self
     */
    private Integer dataPermission;

    /**
     * 权限code列表
     */
    private List<String> permissionCodes;

    public boolean getStatus() {
        return this.status;
    }
}
