package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.domain.vo.telemetry.Error;
import lombok.Data;

import java.util.List;

@Data
public class SiteVO {

    /**
     * 站点ID
     */
    private String siteId;

    /**
     * 在线状态 0-无设备 1-全部离线 2-部分在线 3-全部在线
     */
    private Integer onlineStatus;

    /**
     * 设备数量
     */
    private Integer deviceCount;

    /**
     * owner名称
     */
    private String ownerName;

    /**
     * 地址ID
     */
    private Integer addressId;

    /**
     * 地址
     */
    private String address;

    /**
     * 错误列表
     */
    private List<ErrorVO> errors;
    
}