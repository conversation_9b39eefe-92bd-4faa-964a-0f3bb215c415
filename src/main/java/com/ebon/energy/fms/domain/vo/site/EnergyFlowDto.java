package com.ebon.energy.fms.domain.vo.site;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.Objects;

/**
 * 能源流转数据传输对象
 */
@Data
public class EnergyFlowDto {

    @JsonProperty("Id")
    private String id;

    @JsonProperty("IsOnline")
    private boolean isOnline;

    @JsonProperty("HasBatteries")
    private boolean hasBatteries;

    @JsonProperty("DateLastStatusReceivedUtc")
    private Instant dateLastStatusReceivedUtc;

    @JsonProperty("DateLastStatusSentLocalTimeString")
    private String dateLastStatusSentLocalTimeString;

    @JsonProperty("Solar")
    private SolarSourcesSummaryDto solar;

    @JsonProperty("BatteryWattsNegativeIsCharging")
    private int batteryWattsNegativeIsCharging;

    @JsonProperty("GridWatts")
    private long gridWatts;

    @JsonProperty("GridStatus")
    private String gridStatus;

    @JsonProperty("BatteryStatus")
    private String batteryStatus;

    @JsonProperty("House")
    private HouseSummaryDto house;

    @JsonProperty("ApplyFlowSafeDataFeature")
    private boolean applyFlowSafeDataFeature;

    /**
     * TODO: 移除此属性，应该用单独的 HasGridTieInverter 和 ShowBatteryWidget 属性替换。
     * 截至 <= 2.16 版本，IsGridTieInverter 似乎与显示电池小部件相关联。
     */
    @JsonProperty("IsGridTieInverter")
    private boolean isGridTieInverter;

    @JsonProperty("DisplayExportWarning")
    private boolean displayExportWarning;

    @JsonProperty("DRM0Enable")
    private boolean DRM0_ENABLE;

    @JsonProperty("TodayYiled")
    private BigDecimal todayPVYiled;

    @JsonProperty("TodayGridImport")
    private BigDecimal todayGridImport;

    @JsonProperty("TodayGridExport")
    private BigDecimal todayGridExport;

    @JsonProperty("TodayBatteryCharged")
    private BigDecimal todayBatteryCharged;

    @JsonProperty("TodayBatteryDischarged")
    private BigDecimal todayBatteryDischarged;

    @JsonProperty("TodayBackUpLoadConsumed")
    private BigDecimal todayBackUpLoadConsumed;

    @JsonProperty("TodayACLoadConsumed")
    private BigDecimal todayACLoadConsumed;

    /**
     * 默认构造函数
     */
    public EnergyFlowDto() {
    }

    /**
     * 全参数构造函数
     */
    public EnergyFlowDto(String id, boolean isOnline, boolean hasBatteries,
                         String batteryStatus,
                         Instant dateLastStatusReceivedUtc,
                         String dateLastStatusSentLocalTimeString,
                         SolarSourcesSummaryDto solar,
                         int batteryWattsNegativeIsCharging,
                         long gridWattsAbsolute, String gridStatus,
                         HouseSummaryDto house, boolean applyFlowSafeDataFeature,
                         boolean isGridTieInverter, boolean displayExportWarning, boolean dRM0Enable) {
        this.id = Objects.requireNonNull(id, "id不能为空");
        this.isOnline = isOnline;
        this.hasBatteries = hasBatteries;
        this.batteryStatus = batteryStatus;
        this.dateLastStatusReceivedUtc = dateLastStatusReceivedUtc;
        this.dateLastStatusSentLocalTimeString = dateLastStatusSentLocalTimeString;
        this.solar = Objects.requireNonNull(solar, "solar不能为空");
        this.batteryWattsNegativeIsCharging = batteryWattsNegativeIsCharging;
        this.gridWatts = gridWattsAbsolute;
        this.gridStatus = gridStatus;
        this.house = Objects.requireNonNull(house, "house不能为空");
        this.applyFlowSafeDataFeature = applyFlowSafeDataFeature;
        this.isGridTieInverter = isGridTieInverter;
        this.displayExportWarning = displayExportWarning;
        this.DRM0_ENABLE = dRM0Enable;
    }

    /**
     * 获取是否有太阳能设备
     *
     * @return 如果有太阳能源则返回true
     */
    @JsonProperty("HasSolar")
    public boolean getHasSolar() {
        return solar != null && solar.getSources() != null && !solar.getSources().isEmpty();
    }

    /**
     * 获取电池功率绝对值
     *
     * @return 电池功率的绝对值
     */
    @JsonProperty("BatteryWatts")
    public int getBatteryWatts() {
        return Math.abs(batteryWattsNegativeIsCharging);
    }
}
