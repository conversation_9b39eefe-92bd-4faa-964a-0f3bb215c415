package com.ebon.energy.fms.util;

import com.ebon.energy.fms.common.enums.SystemStatusEnum;
import com.ebon.energy.fms.domain.vo.DailyEnergyTotalsVO;
import com.ebon.energy.fms.domain.vo.WattHour;
import com.ebon.energy.fms.domain.vo.telemetry.BatteryStatus;
import com.ebon.energy.fms.domain.vo.telemetry.SystemStatus;

import java.math.BigDecimal;

public class SystemStatusHelper {

        public static BatteryStatus getBatterySummary(SystemStatus sys) {
                if (sys == null) {
                        return null;
                }

                if (sys.getBattery() != null && (sys.getBattery()
                                .getStatus() == SystemStatusEnum.BatteryStatusValue.Disconnected
                                || sys.getBattery().getStatus() == SystemStatusEnum.BatteryStatusValue.Disabled)) {
                        return sys.getBattery().clone();
                }

                if ((sys.getInverter() == null || sys.getInverter().getBatteryMeasurements() == null)
                                && sys.getBattery() != null
                                || ((sys.getOuijaBoard() != null && sys.getOuijaBoard().getBMSComms() != null
                                                && sys.getOuijaBoard().getBMSComms()) && sys.getBattery() != null)) {
                        return sys.getBattery().clone();
                }

                if (sys.getInverter() == null || sys.getInverter().getBatteryMeasurements() == null) {
                        BatteryStatus batteryStatus = new BatteryStatus();
                        batteryStatus.setStatus(SystemStatusEnum.BatteryStatusValue.Disconnected);
                        return batteryStatus;
                }

                return sys.getInverter().getBatteryMeasurements().clone();
        }

        public static DailyEnergyTotalsVO getDailyEnergyTotals(SystemStatus status) {
                BigDecimal dayTotalE = status.getPV() != null && status.getPV().getDayTotalE() != null
                                ? BigDecimalUtl.toBigDecimal(status.getPV().getDayTotalE())
                                : null;
                BigDecimal thirdDayTotalE = status.getThirdPartyInverter() != null
                                && status.getThirdPartyInverter().getDayTotalE() != null
                                                ? BigDecimalUtl.toBigDecimal(
                                                                status.getThirdPartyInverter().getDayTotalE())
                                                : null;
                WattHour solarRedbackPvDayTotalE = status.getPV() != null && status.getPV().getDayTotalE() != null
                                ? new WattHour(dayTotalE.multiply(new BigDecimal("1000")))
                                : null;

                WattHour solarThirdPartyDayTotalE = status.getThirdPartyInverter() != null
                                && status.getThirdPartyInverter().getDayTotalE() != null
                                                ? new WattHour(thirdDayTotalE.multiply(new BigDecimal("1000")))
                                                : null;

                WattHour solarDayTotalE;
                if (status.getThirdPartyInverter() != null && status.getThirdPartyInverter().getDayTotalE() != null
                                && status.getPV() != null && status.getPV().getDayTotalE() != null) {
                        solarDayTotalE = new WattHour(thirdDayTotalE.multiply(new BigDecimal("1000"))
                                        .add(dayTotalE.multiply(new BigDecimal("1000"))));
                } else {
                        solarDayTotalE = null;
                }

                WattHour loadDayTotalAcE = status.getACLoad() != null && status.getACLoad().getDayTotalE() != null
                                ? new WattHour(BigDecimalUtl.toBigDecimal(status.getACLoad().getDayTotalE())
                                                .multiply(new BigDecimal("1000")))
                                : null;

                WattHour loadDayTotalBackupE = status.getBackupLoad() != null
                                && status.getBackupLoad().getDayTotalE() != null
                                                ? new WattHour(BigDecimalUtl
                                                                .toBigDecimal(status.getBackupLoad().getDayTotalE())
                                                                .multiply(new BigDecimal("1000")))
                                                : null;

                WattHour gridDayTotalImportE = status.getGrid() != null && status.getGrid().getDayTotalImportE() != null
                                ? new WattHour(BigDecimalUtl.toBigDecimal(status.getGrid().getDayTotalImportE())
                                                .multiply(new BigDecimal("1000")))
                                : null;

                WattHour gridDayTotalExportE = status.getGrid() != null && status.getGrid().getDayTotalExportE() != null
                                ? new WattHour(BigDecimalUtl.toBigDecimal(status.getGrid().getDayTotalExportE())
                                                .multiply(new BigDecimal("1000")))
                                : null;

                WattHour batteryDayTotalImportE = status.getInverter() != null
                                && status.getInverter().getBatteryMeasurements() != null
                                && status.getInverter().getBatteryMeasurements().getDayTotalInputE() != null
                                                ? new WattHour(BigDecimalUtl.toBigDecimal(status.getInverter()
                                                                .getBatteryMeasurements().getDayTotalInputE())
                                                                .multiply(new BigDecimal("1000")))
                                                : null;

                WattHour batteryDayTotalExportE = status.getInverter() != null
                                && status.getInverter().getBatteryMeasurements() != null
                                && status.getInverter().getBatteryMeasurements().getDayTotalOutputE() != null
                                                ? new WattHour(BigDecimalUtl.toBigDecimal(status.getInverter()
                                                                .getBatteryMeasurements().getDayTotalOutputE())
                                                                .multiply(new BigDecimal("1000")))
                                                : null;

                return new DailyEnergyTotalsVO(solarDayTotalE, solarRedbackPvDayTotalE, solarThirdPartyDayTotalE,
                                loadDayTotalAcE, loadDayTotalBackupE, gridDayTotalImportE, gridDayTotalExportE,
                                batteryDayTotalImportE, batteryDayTotalExportE);
        }

        public static boolean isOnGrid(SystemStatus status) {
                if (status != null && status.getGrid() != null && status.getGrid().getStatus() != null) {
                        return status.getGrid().getStatus() != SystemStatusEnum.GridStatusValue.Disconnected;
                }
                return false;
        }
}
