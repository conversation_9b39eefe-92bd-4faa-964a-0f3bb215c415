package com.ebon.energy.fms.util;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Supplier;

@Slf4j
public class LocalCache {

    //缓存Map
    private static Map<String, CacheContent> map = new HashMap<>();

    private static LocalCache localCache = new LocalCache();

    private static volatile ConcurrentMap<String, ReentrantLock> lockMap = new ConcurrentHashMap();

    private LocalCache() {
    }

    public static LocalCache getInStance() {
        return localCache;
    }

    public <T> T getLocalCache(String key) {
        CacheContent<T> cc = map.get(key);
        if (null == cc) {
            return null;
        }

        long currentTime = System.currentTimeMillis();
        if (cc.getCacheMillis() > 0 && currentTime - cc.getCreateTime() > cc.getCacheMillis()) {
            //超过缓存过期时间,返回null
            map.remove(key);
            return null;
        } else {
            return cc.getData();
        }
    }

    public <T> void setLocalCache(String key, T value, int cacheMillis) {
        long currentTime = System.currentTimeMillis();
        CacheContent cc = new CacheContent(cacheMillis, value, currentTime);
        map.put(key, cc);
    }

    /**
     * 查询缓存
     *
     * @param key
     * @param supplier
     * @param expireTime 单位秒
     * @param <T>
     * @return
     */
    public static <T> T getOrCreate(String key, Supplier<T> supplier, int expireTime) {
        LocalCache localCache = getInStance();
        T result = localCache.getLocalCache(key);
        if (Objects.nonNull(result)) {
            return result;
        } else {
            T supplierResult = supplier.get();
            localCache.setLocalCache(key, supplierResult, expireTime * 1000);
            return supplierResult;
        }
    }

    /**
     * 查询缓存（防击穿）
     *
     * @param key
     * @param supplier
     * @param expireTime
     * @param <T>
     * @return
     */
    public static <T> T getOrCreateWithLock(String key, Supplier<T> supplier, int expireTime) {
        LocalCache localCache = getInStance();
        T result = localCache.getLocalCache(key);
        if (Objects.nonNull(result)) {
            return result;
        }
        ReentrantLock lock = null;
        try {
            try {
                lock = getLockByKey(key);
                if (null != lock) {
                    lock.lock();
                }
            } catch (Throwable e) {
                log.error(e.getMessage(), e);
            }
            result = localCache.getLocalCache(key);
            if (null == result) {
                result = supplier.get();
                localCache.setLocalCache(key, result, expireTime * 1000);
            }
        } finally {
            if (null != lock) {
                lock.unlock();
                remove(key);
            }
        }

        return result;
    }

    public static void delete(String key) {
        map.remove(key);
    }

    public synchronized static void deleteByPrefix(String key) {
        List<String> keys = new ArrayList<>();
        map.keySet().forEach(e -> {
            if (e.startsWith(key)) {
                keys.add(e);
            }
        });

        keys.forEach(e -> {
            map.remove(e);
        });
    }

    /**
     * 非线程安全，在高并发下，有可能出现一个key对应几个lock
     *
     * @param key
     * @return
     */
    public static ReentrantLock getLockByKey(String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }

        ReentrantLock lock = lockMap.get(key);
        if (null == lock) {
            lock = new ReentrantLock(true);
            ReentrantLock prevLock = lockMap.putIfAbsent(key, lock);
            if (null != prevLock) {
                return prevLock;
            }
        }
        return lock;
    }

    /**
     * 删除锁
     *
     * @param lockKey
     */
    public static void remove(String lockKey) {
        lockMap.remove(lockKey);
    }

    @Getter
    @Setter
    @AllArgsConstructor
    class CacheContent<T> {
        /**
         * 缓存有效时间
         */
        private int cacheMillis;

        /**
         * 缓存对象
         */
        private T data;

        /**
         * 缓存创建时间
         */
        private long createTime;
    }

}