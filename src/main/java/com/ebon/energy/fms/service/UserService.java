package com.ebon.energy.fms.service;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ebon.energy.fms.common.enums.CommonErrorCodeEnum;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.entity.FmsRoleDO;
import com.ebon.energy.fms.domain.entity.FmsUserDO;
import com.ebon.energy.fms.domain.entity.FmsUserRoleDO;
import com.ebon.energy.fms.domain.po.CreateUserPO;
import com.ebon.energy.fms.domain.po.ModifyUserPO;
import com.ebon.energy.fms.domain.po.UpdatePasswordPO;
import com.ebon.energy.fms.domain.po.UserListPO;
import com.ebon.energy.fms.domain.vo.PageResult;
import com.ebon.energy.fms.domain.vo.UserListVO;
import com.ebon.energy.fms.domain.vo.UserVO;
import com.ebon.energy.fms.mapper.primary.RoleMapper;
import com.ebon.energy.fms.mapper.primary.UserMapper;
import com.ebon.energy.fms.mapper.primary.UserRoleMapper;
import com.ebon.energy.fms.util.CookieUtil;
import com.ebon.energy.fms.util.MD5Util;
import com.ebon.energy.fms.util.RequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;

import static com.ebon.energy.fms.common.constants.AuthorizeConstants.BASE_SALT;
import static com.ebon.energy.fms.common.constants.AuthorizeConstants.COOKIE_TOKEN_KEY;

@Slf4j
@Service
public class UserService {

    @Resource
    private JwtService jwtService;

    @Resource
    private UserMapper userMapper;

    @Resource
    private UserRoleMapper userRoleMapper;

    @Resource
    private RoleMapper roleMapper;

    @Resource
    private FleetMonitoringUsersService fleetMonitoringUsersService;

    @Value("${fmsSystemDefaultPassword}")
    private String fmsSystemDefaultPassword;

    public void login(String email, String password, HttpServletResponse response) {
        FmsUserDO fmsUserDO = userMapper.selectByEmail(email);
        if (fmsUserDO == null) {
            throw new BizException(CommonErrorCodeEnum.USER_NOT_EXIST);
        }

        if (!checkPassword(email, password, fmsUserDO.getPassword())) {
            throw new BizException(CommonErrorCodeEnum.PASSWORD_ERROR);
        }

        if (!fmsUserDO.getStatus()) {
            throw new BizException(CommonErrorCodeEnum.USER_DISABLED);
        }

        String jwtToken = jwtService.generateToken(fmsUserDO.getEmail());
        CookieUtil.create(response, COOKIE_TOKEN_KEY, jwtToken, false);

        // 老系统生成此用户
        if (!fleetMonitoringUsersService.existInOldVersion(email)) {
            fleetMonitoringUsersService.addUserInOldVersion(email);
        }
        fmsUserDO.setLastLoginTime(new Timestamp(System.currentTimeMillis()));
        userMapper.updateById(fmsUserDO);
    }

    public void loginOut(HttpServletResponse response) {
        CookieUtil.clear(response, COOKIE_TOKEN_KEY);
    }


    private boolean checkPassword(String email, String inputPassword, String passwordInDb) {
        return getPasswordWithSalt(inputPassword, email).equals(passwordInDb);
    }

    public UserVO getUserByEmail(String email) {
        FmsUserDO userDO = userMapper.selectByEmail(email);
        if(userDO == null){
            return null;
        }

        return UserVO.builder().userId(userDO.getId()).email(userDO.getEmail())
                .name(userDO.getName()).status(userDO.getStatus())
                .portalUserId(userDO.getPortalUserId()).build();
    }

    private String getPasswordWithSalt(String password, String salt) {
        return MD5Util.getMD5(salt + password + BASE_SALT);
    }

    public PageResult<UserListVO> getUserList(UserListPO userListPO) {
        int offset = (userListPO.getCurrent() - 1) * userListPO.getPageSize();
        List<UserListVO> userListVOs = userMapper.selectUserList(userListPO.getUserName(), userListPO.getEmail(), userListPO.getRoleId(), offset, userListPO.getPageSize());
        long total = userMapper.countUserList(userListPO.getUserName(), userListPO.getEmail(), userListPO.getRoleId());
        return PageResult.toResponse(userListVOs, total, userListPO.getCurrent(), userListPO.getPageSize());
    }

    public void createUser(CreateUserPO createUserPO) {
        FmsUserDO userExist = userMapper.selectByEmail(createUserPO.getEmail());
        if (Objects.nonNull(userExist)) {
            throw new BizException(CommonErrorCodeEnum.USER_ALREADY_EXISTS);
        }
        FmsRoleDO roleExist = roleMapper.findByRoleId(createUserPO.getRoleId());
        if (Objects.isNull(roleExist)) {
            throw new BizException(CommonErrorCodeEnum.ROLE_NOT_EXIST);
        }
        if (!roleExist.isEnable()) {
            throw new BizException(CommonErrorCodeEnum.ROLE_UNAVAILABLE);
        }
        FmsUserDO user = new FmsUserDO();
        user.setName(createUserPO.getName());
        user.setEmail(createUserPO.getEmail());
        user.setStatus(createUserPO.getStatus());
        user.setPassword(MD5Util.getMD5(createUserPO.getEmail() + fmsSystemDefaultPassword + BASE_SALT));
        userMapper.insert(user);
        userRoleMapper.bindUserRole(user.getId(), createUserPO.getRoleId());

        // 老系统生成此用户
        if (!fleetMonitoringUsersService.existInOldVersion(createUserPO.getEmail())) {
            fleetMonitoringUsersService.addUserInOldVersion(createUserPO.getEmail());
        }
    }

    public void modifyUser(ModifyUserPO modifyUserPO) {
        FmsUserDO userExist = userMapper.selectById(modifyUserPO.getId());
        if (Objects.isNull(userExist)) {
            throw new BizException(CommonErrorCodeEnum.USER_NOT_EXIST);
        }
        FmsRoleDO roleExist = roleMapper.findByRoleId(modifyUserPO.getRoleId());
        if (Objects.isNull(roleExist)) {
            throw new BizException(CommonErrorCodeEnum.ROLE_NOT_EXIST);
        }
        if (!roleExist.isEnable()) {
            throw new BizException(CommonErrorCodeEnum.ROLE_UNAVAILABLE);
        }
        userMapper.modifyUser(modifyUserPO.getId(), modifyUserPO.getName(), modifyUserPO.getStatus());
        FmsUserRoleDO fmsUserRoleDO = userRoleMapper.findByUserId(modifyUserPO.getId());
        if (Objects.isNull(fmsUserRoleDO)) {
            userRoleMapper.bindUserRole(modifyUserPO.getId(), modifyUserPO.getRoleId());
        } else {
            userRoleMapper.modifyUserRole(modifyUserPO.getId(), modifyUserPO.getRoleId());
        }
    }

    public void updatePassword(UpdatePasswordPO updatePasswordPO) {
        String email = RequestUtil.getLoginUserEmail();
        FmsUserDO currentUser = userMapper.selectByEmail(email);
        if (!checkPassword(currentUser.getEmail(), updatePasswordPO.getOldPassword(), currentUser.getPassword())) {
            throw new BizException(CommonErrorCodeEnum.PASSWORD_ERROR);
        }
        String newPasswordMd5 = MD5Util.getMD5(currentUser.getEmail() + updatePasswordPO.getNewPassword() + BASE_SALT);
        currentUser.setPassword(newPasswordMd5);
        userMapper.updateById(currentUser);
    }
}