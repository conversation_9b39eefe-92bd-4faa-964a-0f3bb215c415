package com.ebon.energy.fms.service.site;

import com.alibaba.fastjson.JSONObject;
import com.ebon.energy.fms.common.enums.ApplicationName;
import com.ebon.energy.fms.common.enums.CloudPlatformName;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.common.utils.Helper;
import com.ebon.energy.fms.common.utils.TimeZoneConverterUtil;
import com.ebon.energy.fms.config.PortalConfig;
import com.ebon.energy.fms.domain.entity.AddressesDO;
import com.ebon.energy.fms.domain.entity.ProductDailyCachesDO;
import com.ebon.energy.fms.domain.vo.DataWithPermalinkVO;
import com.ebon.energy.fms.domain.vo.DeviceVO;
import com.ebon.energy.fms.domain.vo.ProductVO;
import com.ebon.energy.fms.domain.vo.product.ProductInfoLightVO;
import com.ebon.energy.fms.domain.vo.site.*;
import com.ebon.energy.fms.domain.vo.product.control.InstallationSpecification;
import com.ebon.energy.fms.domain.entity.ProductDailyHistoryDO;
import com.ebon.energy.fms.domain.vo.telemetry.DataWithLinks;
import com.ebon.energy.fms.domain.vo.telemetry.SystemStatus;
import com.ebon.energy.fms.mapper.third.AddressesMapper;
import com.ebon.energy.fms.mapper.third.ProductMapper;
import com.ebon.energy.fms.repository.ProductDbRepository;
import com.ebon.energy.fms.repository.impl.SiteRepository;
import com.ebon.energy.fms.service.ProductService;
import com.ebon.energy.fms.service.SiteDeviceService;
import com.ebon.energy.fms.service.SiteService;
import com.ebon.energy.fms.service.factory.EnergyFlowFactory;
import com.ebon.energy.fms.service.factory.ISiteConfigurationAggregator;
import com.ebon.energy.fms.service.factory.SiteConfigurationAggregatorFactory;
import com.ebon.energy.fms.util.ConfigurationUtil;
import com.ebon.energy.fms.repository.impl.SpecificationRepository;
import com.ebon.energy.fms.mapper.third.ProductDailyCachesMapper;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.LogicalTypes;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ebon.energy.fms.util.SafeAccess.getValue;

@Slf4j
@Service
@RequiredArgsConstructor
public class SiteEnergyService {

    private final SiteService siteService;

    private final SiteRepository siteRepository;

    private final SiteDeviceService siteDeviceService;

    private final SiteConfigurationAggregatorFactory siteConfigurationAggregatorFactory;

    private final SpecificationRepository specificationRepository;

    private final ProductDbRepository productDbRepository;

    private final ProductMapper productMapper;

    private final AddressesMapper addressesMapper;

    private final ProductDailyCachesMapper productDailyCachesMapper;

    private final ProductService productService;

    public EnergyFlowDto getEnergyFlow(String siteId) {
        var exportLimitMessageThresholdW = ConfigurationUtil.getPortalConfig().getExportLimitMessageThresholdW();
        return getSiteEnergyFlow(siteId, exportLimitMessageThresholdW);
    }

    public BatteryStatusDto getBatteryStatus(String siteId) {
        var portalConfig = ConfigurationUtil.getPortalConfig();
        var globalCurrentGridExportLimit = portalConfig.getExportLimitMessageThresholdW();
        var numberOfConsecutiveDaysBatteriesMismatchedToDisabled = portalConfig.getNumberOfConsecutiveDaysBatteriesMismatchedToDisabled();
        var siteDevices = siteDeviceService.getSiteDevices(siteId);
        var siteAggregator = siteConfigurationAggregatorFactory.getSiteAggregator(siteId, siteDevices);
        var allAbout = siteAggregator.processSiteOverview(siteId, siteDevices);
        if (allAbout.getDataForDashboard() == null) {
            return null;
        }
        var status = siteAggregator.processSiteBatteryStatus(
                allAbout,
                getSiteEnergyFlow(siteId, globalCurrentGridExportLimit),
                numberOfConsecutiveDaysBatteriesMismatchedToDisabled);

        if (status == null) {
            return null;
        }

        var updateSocTo100 = isUpdateSocTo100(siteId, ConfigurationUtil.getPortalConfig().getBatteryFullThreshold(), status.getSoC0to1(), status.getState());

        if (updateSocTo100) {
            status.setSoC0to1(BigDecimal.ONE);
        }

        return status;
    }

    public RenewableGaugeDto getRenewableGauge(String siteId) {
        var portalConfig = ConfigurationUtil.getPortalConfig();
        var siteDevices = siteDeviceService.getSiteDevices(siteId);
        var siteAggregator = siteConfigurationAggregatorFactory.getSiteAggregator(siteId, siteDevices);
        var allAbout = siteAggregator.processSiteOverview(siteId, siteDevices);
        if (allAbout.getIsAcCoupledMode()) {
            return null;
        } else {
            return getRenewableGaugeInternal(siteId, null, siteDevices);
        }
    }


    public EnergyFlowDto getSiteEnergyFlow(String siteId, Integer globalCurrentGridExportLimit) {
        var siteDevices = siteDeviceService.getSiteDevices(siteId);
        var siteAggregator = siteConfigurationAggregatorFactory.getSiteAggregator(siteId, siteDevices);
        var allAbout = siteAggregator.processSiteOverview(siteId, siteDevices);
        if (allAbout == null || allAbout.getDataForDashboard() == null) {
            return null;
        }

        var energyFlowExtended = EnergyFlowFactory.buildForViewModels(allAbout.getDataForDashboard(),
                allAbout.getHasBatteries(), allAbout.getHasSupportForConnectedPV());

        var energyFlowDto = Helper.getEnergyFlowFromParts(
                siteId,
                energyFlowExtended,
                siteDevices.stream().map(AllAboutDevice::getSpecification).collect(Collectors.toList()),
                allAbout.getDataForDashboard().getSiteExportLimitW(),
                globalCurrentGridExportLimit
        );
        fillEnergyFlowWithTodayGeneration(energyFlowDto, siteDevices.stream().map(AllAboutDevice::getSerialNumber).collect(Collectors.toList()));
        return energyFlowDto;
    }

    public RenewableGaugeDto getRenewableGaugeInternal(String publicSiteId, String loggedInUserId, List<AllAboutDevice> siteDevices) {
        try {
            ISiteConfigurationAggregator siteAggregator = siteConfigurationAggregatorFactory.getSiteAggregator(publicSiteId, siteDevices);
            LocalDate todayLocalTime = siteRepository.getBclTimeZoneIdForSite(publicSiteId);
            LocalDate startOfToday = todayLocalTime;

            List<ProductWithDailyCache> productsWithDailies = new ArrayList<>();

            // For each device, populate productWithDailies from database
            for (AllAboutDevice device : siteDevices) {
                try {
                    InstallationSpecification installSpecs = specificationRepository.getInstallationSpecAsync(device.getSerialNumber());

                    // Get 91 days of data (90 + 1 for today, which we will drop to avoid partial results)
                    LocalDate startDate = startOfToday.minusDays(90);
                    Timestamp startTime = Timestamp.valueOf(startDate.atStartOfDay());
                    Timestamp endTime = Timestamp.valueOf(startOfToday.atStartOfDay());

                    var dailyCaches = productDbRepository.getNDayProductDailyCacheTrusted(
                            device.getSerialNumber(), startTime, endTime);

                    // Filter out today to avoid partial results
                    var filteredCaches = dailyCaches.stream()
                            .filter(d -> d.getTime().isBefore(startOfToday.atStartOfDay()))
                            .collect(Collectors.toList());

                    ProductWithDailyCache productWithDailies = new ProductWithDailyCache(
                            device.getSerialNumber(),
                            device,
                            installSpecs,
                            filteredCaches);
                    productsWithDailies.add(productWithDailies);
                } catch (Exception ex) {
                    log.error("Error collecting GetRenewableGauge data for device '{}' of site '{}'", device.getSerialNumber(), publicSiteId, ex);
                    return null;
                }
            }

            BigDecimal percent90DaysDouble = siteAggregator.processSiteRenewablePercent(publicSiteId, productsWithDailies);
            Integer percent90Days = percent90DaysDouble != null ? Math.round(percent90DaysDouble.floatValue()) : null;

            // Calc the 30 day consumption
            LocalDate thirtyDaysAgo = startOfToday.minusDays(30);
            List<ProductWithDailyCache> thirtyDayBatch = new ArrayList<>();
            for (ProductWithDailyCache prodData : productsWithDailies) {
                var filteredThirtyDays = prodData.getDailyCaches().stream()
                        .filter(d -> !d.getTime().isBefore(thirtyDaysAgo.atStartOfDay()))
                        .collect(Collectors.toList());

                thirtyDayBatch.add(new ProductWithDailyCache(
                        prodData.getSerialNumber(),
                        prodData.getBaseAboutDevice(),
                        prodData.getInstallationSpecification(),
                        filteredThirtyDays));
            }

            BigDecimal percent30DaysDouble = siteAggregator.processSiteRenewablePercent(publicSiteId, thirtyDayBatch);
            Integer percent30Days = percent30DaysDouble != null ? Math.round(percent30DaysDouble.floatValue()) : null;

            // Calc the 7 Day consumption
            LocalDate sevenDaysAgo = startOfToday.minusDays(7);
            List<ProductWithDailyCache> sevenDayBatch = new ArrayList<>();
            for (ProductWithDailyCache prodData : thirtyDayBatch) {
                var filteredSevenDays = prodData.getDailyCaches().stream()
                        .filter(d -> !d.getTime().isBefore(sevenDaysAgo.atStartOfDay()))
                        .collect(Collectors.toList());

                sevenDayBatch.add(new ProductWithDailyCache(
                        prodData.getSerialNumber(),
                        prodData.getBaseAboutDevice(),
                        prodData.getInstallationSpecification(),
                        filteredSevenDays));
            }

            BigDecimal percent07DaysDouble = siteAggregator.processSiteRenewablePercent(publicSiteId, sevenDayBatch);
            Integer percent07Days = percent07DaysDouble != null ? Math.round(percent07DaysDouble.floatValue()) : null;

            // Calc the Today consumption
            List<ProductWithDailyCache> todayBatch = new ArrayList<>();
            for (ProductWithDailyCache prodData : thirtyDayBatch) {
                var filteredToday = prodData.getDailyCaches().stream()
                        .filter(d -> !d.getTime().isBefore(startOfToday.atStartOfDay()))
                        .collect(Collectors.toList());

                todayBatch.add(new ProductWithDailyCache(
                        prodData.getSerialNumber(),
                        prodData.getBaseAboutDevice(),
                        prodData.getInstallationSpecification(),
                        filteredToday));
            }

            BigDecimal percentTodayDouble = siteAggregator.processSiteRenewablePercent(publicSiteId, todayBatch);
            Integer percentToday = percentTodayDouble != null ? Math.round(percentTodayDouble.floatValue()) : null;

            return new RenewableGaugeDto(publicSiteId, percentToday, percent07Days, percent30Days, percent90Days);

        } catch (Exception ex) {
            log.error("Error in getRenewableGaugeInternal for site '{}'", publicSiteId, ex);
            return null;
        }
    }

    private boolean isUpdateSocTo100(String siteId, BigDecimal batteryFullThreshold, BigDecimal soc, String status) {
        var products = siteRepository.getSiteProducts(siteId);

        if (batteryFullThreshold == null || batteryFullThreshold == null) {
            return false;
        }

        if (Optional.ofNullable(soc).orElse(BigDecimal.ZERO).compareTo(batteryFullThreshold) < 0) {
            return false;
        }

        var productSystemStatuses = products.stream()
                .map(it -> JSONObject.parseObject(productMapper.getProductInfo(it.getNumber()).getLatestSystemStatus(), SystemStatus.class))
                .collect(Collectors.toList());
        if (productSystemStatuses.stream()
                .map(p -> p.getBattery() != null ? p.getBattery().getBatteryModel() : null)
                .anyMatch(model -> "RB600".equals(model))
                && soc.compareTo(batteryFullThreshold) >= 0
                && (status == null || !"charging".equalsIgnoreCase(status))) {
            return true;
        }

        return false;
    }

    private void fillEnergyFlowWithTodayGeneration(EnergyFlowDto energyFlowDto, List<String> sns) {
        if (CollectionUtils.isEmpty(sns)) {
            return;
        }
        energyFlowDto.setTodayPVYiled(calculateTotalGeneration(sns));

        BigDecimal todayGridImport = BigDecimal.ZERO;

        BigDecimal todayGridExport = BigDecimal.ZERO;

        BigDecimal todayBatteryCharged = BigDecimal.ZERO;

        BigDecimal todayBatteryDischarged = BigDecimal.ZERO;

        BigDecimal todayBackUpLoadConsumed = BigDecimal.ZERO;

        BigDecimal todayACLoadConsumed = BigDecimal.ZERO;

        for (var product : productService.getProducts(sns)) {
            if (StringUtils.isBlank(product.getLatestSystemStatus())) {
                continue;
            }
            var oldSystemStatus = JSONObject.parseObject(product.getLatestSystemStatus(), SystemStatus.class);

            if (oldSystemStatus.getGrid() != null && oldSystemStatus.getGrid().getDayTotalImportE() != null) {
                todayGridImport = todayGridImport.add(new BigDecimal(oldSystemStatus.getGrid().getDayTotalImportE()));
            }
            if (oldSystemStatus.getGrid() != null && oldSystemStatus.getGrid().getDayTotalExportE() != null) {
                todayGridExport = todayGridExport.add(new BigDecimal(oldSystemStatus.getGrid().getDayTotalExportE()));
            }
            if (oldSystemStatus.getInverter() != null && oldSystemStatus.getInverter().getBatteryMeasurements() != null
                    && oldSystemStatus.getInverter().getBatteryMeasurements().getDayTotalInputE() != null) {
                todayBatteryCharged = todayBatteryCharged.add(new BigDecimal(oldSystemStatus.getInverter().getBatteryMeasurements().getDayTotalInputE()));
            }
            if (oldSystemStatus.getInverter() != null && oldSystemStatus.getInverter().getBatteryMeasurements() != null
                    && oldSystemStatus.getInverter().getBatteryMeasurements().getDayTotalOutputE() != null) {
                todayBatteryDischarged = todayBatteryDischarged.add(new BigDecimal(oldSystemStatus.getInverter().getBatteryMeasurements().getDayTotalOutputE()));
            }
            if (oldSystemStatus.getBackupLoad() != null && oldSystemStatus.getBackupLoad().getDayTotalE() != null) {
                todayBackUpLoadConsumed = todayBackUpLoadConsumed.add(new BigDecimal(oldSystemStatus.getBackupLoad().getDayTotalE()));
            }
            if (oldSystemStatus.getACLoad() != null && oldSystemStatus.getACLoad().getDayTotalE() != null) {
                todayACLoadConsumed = todayACLoadConsumed.add(new BigDecimal(oldSystemStatus.getACLoad().getDayTotalE()));
            }
        }

        energyFlowDto.setTodayGridImport(todayGridImport.setScale(4, RoundingMode.HALF_UP));
        energyFlowDto.setTodayGridExport(todayGridExport.setScale(4, RoundingMode.HALF_UP));
        energyFlowDto.setTodayBatteryCharged(todayBatteryCharged.setScale(4, RoundingMode.HALF_UP));
        energyFlowDto.setTodayBatteryDischarged(todayBatteryDischarged.setScale(4,  RoundingMode.HALF_UP));
        energyFlowDto.setTodayBackUpLoadConsumed(todayBackUpLoadConsumed.setScale(4,RoundingMode.HALF_UP));
        energyFlowDto.setTodayACLoadConsumed(todayACLoadConsumed.setScale(4,RoundingMode.HALF_UP));

    }


    /**
     * calculate sites total generation for today
     *
     * @param sns
     * @return
     */
    private BigDecimal calculateTotalGeneration(List<String> sns) {
        return sns.stream().map(sn -> {
            var addressDO = addressesMapper.selectSiteAddressBySn(sn);
            var today = addressDO != null && StringUtils.isNoneBlank(addressDO.getTimeZoneId()) ?
                    TimeZoneConverterUtil.getLocalDateFromBclTimeZone(Instant.now(), addressDO.getTimeZoneId())
                    : LocalDate.now();
            var formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
            var dateStr = today.format(formatter);

            return Optional.ofNullable(productDailyCachesMapper.selectDayTotalGeneration(sn, dateStr)).orElse(BigDecimal.ZERO);
        }).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

}
