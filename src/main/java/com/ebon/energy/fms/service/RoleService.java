package com.ebon.energy.fms.service;

import com.ebon.energy.fms.common.enums.CommonErrorCodeEnum;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.entity.FmsRoleDO;
import com.ebon.energy.fms.domain.po.RoleListPO;
import com.ebon.energy.fms.domain.po.RoleModifyPO;
import com.ebon.energy.fms.domain.po.RolePO;
import com.ebon.energy.fms.domain.vo.PageResult;
import com.ebon.energy.fms.domain.vo.RoleDetailVO;
import com.ebon.energy.fms.domain.vo.RoleDropDownVO;
import com.ebon.energy.fms.domain.vo.RoleVO;
import com.ebon.energy.fms.mapper.primary.RoleMapper;
import com.ebon.energy.fms.mapper.primary.UserRoleMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class RoleService {

    @Resource
    private RoleMapper roleMapper;

    @Resource
    private UserRoleMapper userRoleMapper;
    
    @Resource
    private PermissionService permissionService;

    public PageResult<RoleVO> getRoleList(RoleListPO roleListPO) {
        int offset = (roleListPO.getCurrent() - 1) * roleListPO.getPageSize();
        List<RoleVO> roleVOS = roleMapper.selectAll(roleListPO.getRoleId(), offset, roleListPO.getPageSize());
        long total = roleMapper.countRoleList(roleListPO.getRoleId());
        return PageResult.toResponse(roleVOS, total, roleListPO.getCurrent(), roleListPO.getPageSize());
    }

    public RoleDetailVO getRoleDetail(Integer roleId) {
        RoleDetailVO detailVO = new RoleDetailVO();
        FmsRoleDO roleDO = roleMapper.findByRoleId(roleId);
        detailVO.setId(roleId);
        detailVO.setRoleName(roleDO.getRoleName());
        detailVO.setStatus(roleDO.isStatus());
        detailVO.setDataPermission(roleDO.getDataPermission());
        
        detailVO.setPermissionCodes(permissionService.getRolePermCodes(roleId));
        return detailVO;
    }

    public void addRole(RolePO rolePO) {
        FmsRoleDO exist = roleMapper.findByRoleName(rolePO.getRoleName());
        if (Objects.nonNull(exist)) {
            throw new BizException(CommonErrorCodeEnum.ROLE_ALREADY_EXISTS);
        }
        
        roleMapper.addNewRole(rolePO.getRoleName(), rolePO.getStatus(), rolePO.getDataPermission() != null ? rolePO.getDataPermission() : 0);

        if (CollectionUtils.isNotEmpty(rolePO.getPermissionCodes())) {
            FmsRoleDO newRole = roleMapper.findByRoleName(rolePO.getRoleName());
            permissionService.saveRolePerms(newRole.getId(), rolePO.getPermissionCodes());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void modifyRole(RoleModifyPO roleModifyPO) {
        FmsRoleDO exist = roleMapper.findByRoleId(roleModifyPO.getRoleId());
        if (Objects.isNull(exist)) {
            throw new BizException(CommonErrorCodeEnum.ROLE_NOT_EXIST);
        }
        // 如果角色名被修改，需保证不与其他角色重名
        if (!Objects.equals(exist.getRoleName(), roleModifyPO.getRoleName())) {
            FmsRoleDO roleWithSameName = roleMapper.findByRoleName(roleModifyPO.getRoleName());
            if (Objects.nonNull(roleWithSameName)) {
                throw new BizException(CommonErrorCodeEnum.ROLE_ALREADY_EXISTS);
            }
        }
        // 如果当前角色正在被使用，则不可禁用
        if (!roleModifyPO.getStatus()) {
            long countUserWithCurrentRole = userRoleMapper.countUserWithRoleId(roleModifyPO.getRoleId());
            if (countUserWithCurrentRole > 0) {
                throw new BizException(CommonErrorCodeEnum.ROLE_CANNOT_BE_DISABLED);
            }
        }

        Integer dataPermission = roleModifyPO.getDataPermission() == null ? exist.getDataPermission() : roleModifyPO.getDataPermission();
        roleMapper.modifyRole(roleModifyPO.getRoleId(), roleModifyPO.getRoleName(), roleModifyPO.getStatus(), dataPermission);
        
        permissionService.updateRolePerms(roleModifyPO.getRoleId(), roleModifyPO.getPermissionCodes());
    }

    public List<RoleDropDownVO> getRoleDropdownList() {
        return roleMapper.selectDropdownList();
    }
}
