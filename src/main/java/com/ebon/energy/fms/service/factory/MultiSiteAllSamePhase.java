package com.ebon.energy.fms.service.factory;

import com.ebon.energy.fms.common.enums.PhaseRole;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.*;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.common.utils.SiteOverviewHelper;
import com.ebon.energy.fms.domain.entity.DailyTotalVO;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.site.*;
import com.ebon.energy.fms.domain.vo.telemetry.BatteryStatus;
import io.vavr.Tuple2;
import io.vavr.Tuple3;
import io.vavr.Tuple4;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class MultiSiteAllSamePhase implements ISiteConfigurationAggregator {

    @Override
    public AllAboutSiteOverview processSiteOverview(String publicSiteId, List<AllAboutDevice> siteDevices) {
        if (siteDevices == null || siteDevices.isEmpty()) {
            return null;
        }

        try {
            // 获取主协调设备（假设列表中存在且唯一）
            AllAboutDevice coordinator = siteDevices.stream()
                    .filter(d -> d.getPhaseRole() == PhaseRole.Coordinator)
                    .findAny()
                    .orElseThrow(() -> new BizException("Coordinator device not found"));

            // 基础属性提取
            boolean hasSupportForConnectedPV = siteDevices.stream()
                    .anyMatch(d -> Objects.nonNull(d.getSpecification()) && d.getSpecification().isSupportsConnectedPV());

            boolean hasSolar = siteDevices.stream().anyMatch(AllAboutDevice::getHasSolar);
            boolean hasBatteries = siteDevices.stream().anyMatch(AllAboutDevice::getHasBatteries);

            LocalDate supportsLoadContributorsSince = siteDevices.stream()
                    .map(AllAboutDevice::getSupportsLoadContributorsSince)
                    .filter(Objects::nonNull)
                    .min(LocalDate::compareTo)
                    .orElse(null);

            WattHour maximumPossiblePVOutput = siteDevices.stream()
                    .map(AllAboutDevice::getMaximumPossiblePVOutput)
                    .filter(Objects::nonNull)
                    .reduce(WattHour.Zero, (a, b) -> a.add(b)); // 假设WattHour支持加法

            boolean batteryMismatchProtectionEnabled = siteDevices.stream()
                    .anyMatch(d -> d.getHasBatteries() && d.getIsBatteryMismatchProtectionEnabled());
            ZonedDateTime lastStatusDateUtc;
            if (siteDevices.stream().anyMatch(it -> it.getDataForDashboard() == null || it.getDataForDashboard().getLastStatusDateUtc() == null)) {
                lastStatusDateUtc = null;
            } else {
                lastStatusDateUtc = siteDevices.stream()
                        .map(d -> d.getDataForDashboard())
                        .filter(Objects::nonNull)
                        .map(DataForDashboardVO::getLastStatusDateUtc)
                        .min(ZonedDateTime::compareTo)
                        .orElse(null);
            }
            boolean measuringThirdPartyInverter = siteDevices.stream()
                    .anyMatch(d -> Objects.nonNull(d.getSpecification()) && d.getSpecification().isMeasuringThirdPartyInverter());

            boolean isAcCoupledMode = siteDevices.stream()
                    .anyMatch(d -> Objects.nonNull(d.getSpecification()) && d.getSpecification().isInAcCoupledMode());

            // 处理DataForDashboard为null的设备
            if (siteDevices.stream().anyMatch(d -> d.getDataForDashboard() == null)) {
                DataForDashboardVO emptySiteData = new DataForDashboardVO(
                        null,
                        "5.0.0",
                        lastStatusDateUtc != null ? lastStatusDateUtc : LocalDateTime.of(1, 1, 1, 0, 0).atZone(ZoneOffset.UTC),
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        false,
                        null,
                        null
                );

                return new AllAboutSiteOverview(
                        publicSiteId,
                        emptySiteData,
                        siteDevices,
                        siteDevices.get(0).getTodaysDateInLocalTime(), // 假设列表非空
                        hasSupportForConnectedPV,
                        maximumPossiblePVOutput,
                        hasSolar,
                        hasBatteries,
                        batteryMismatchProtectionEnabled,
                        supportsLoadContributorsSince,
                        measuringThirdPartyInverter,
                        isAcCoupledMode
                );
            }

            // 组合电池状态
            List<BatteryStatus> batterySummaries = siteDevices.stream()
                    .filter(AllAboutDevice::getHasBatteries)
                    .map(AllAboutDevice::getDataForDashboard)
                    .map(DataForDashboardVO::getBatterySummary)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            BatteryStatus batteryStatus;
            boolean bmsVersionMismatch;
            if (!batterySummaries.isEmpty()) {
                Tuple2<BatteryStatus, Boolean> result = SiteOverviewHelper.combineBatteryStatuses(batterySummaries);
                batteryStatus = result._1;
                bmsVersionMismatch = result._2;
            } else {
                batteryStatus = new BatteryStatus();
                bmsVersionMismatch = false;
            }

            // 组合其他状态（假设SiteOverviewHelper方法返回对应类型）
            Tuple2<Watt, GridStatusValue> grid = SiteOverviewHelper.combineGridStatusesOnSamePhase(siteDevices);
            Tuple4<String, String, String, Watt> inverterStatus = SiteOverviewHelper.combineInverterStatuses(
                    siteDevices.stream().map(AllAboutDevice::getDataForDashboard).collect(Collectors.toList()));
            Tuple2<Watt, BigDecimal> thirdPartyInverterStatus = SiteOverviewHelper.combineThirdPartyInverterStatuses(
                    siteDevices.stream().map(AllAboutDevice::getDataForDashboard).collect(Collectors.toList()));
            Tuple2<Watt, BigDecimal> pvStatus = SiteOverviewHelper.combinePVStatuses(
                    siteDevices.stream().map(AllAboutDevice::getDataForDashboard).collect(Collectors.toList()));
            DailyEnergyTotalsVO dayTotalsE = SiteOverviewHelper.combineDayTotalsOnSamePhase(siteDevices);
            Tuple2<Watt, Status> backupLoadStatus = SiteOverviewHelper.combineBackupLoadStatuses(
                    siteDevices.stream().map(AllAboutDevice::getDataForDashboard).collect(Collectors.toList()));
            Tuple3<ZonedDateTime, String, Boolean> ouijaBoard = SiteOverviewHelper.combineOuijaBoardDetails(
                    siteDevices.stream().map(AllAboutDevice::getDataForDashboard).collect(Collectors.toList()));
            Tuple2<Watt, BigDecimal> acLoadStatus = SiteOverviewHelper.combineACLoadStatuses(
                    inverterStatus._4,
                    thirdPartyInverterStatus._1,
                    grid._1,
                    backupLoadStatus._1
            );

            // 从主协调设备获取出口限制
            Integer siteExportLimitW = coordinator.getDataForDashboard().getSiteExportLimitW();

            // 处理离线状态
            boolean siteOffLine = SiteOverviewHelper.isOffline(lastStatusDateUtc);
            if (siteOffLine || (hasBatteries && (
                    batteryStatus.getStatus() == BatteryStatusValue.Disabled ||
                            batteryStatus.getStatus() == BatteryStatusValue.Disconnected))) {

                siteExportLimitW = null;

                pvStatus = new Tuple2<>(Watt.Zero, null);
                thirdPartyInverterStatus = new Tuple2<>(Watt.Zero, null);
                acLoadStatus = new Tuple2<>(Watt.Zero, null);
                backupLoadStatus = new Tuple2<>(Watt.Zero, Status.Off);

                batteryStatus = new BatteryStatus();
                batteryStatus.setStatus(siteOffLine ? BatteryStatusValue.Idle : batteryStatus.getStatus());
                batteryStatus.setP(0.0);
                batteryStatus.setI(0.0);
                batteryStatus.setSoC(100.0);

                inverterStatus = new Tuple4<>("", "", "", Watt.Zero);
                grid = new Tuple2<>(Watt.Zero, GridStatusValue.Idle);
                dayTotalsE = null;
            }

            DataForDashboardVO siteData = new DataForDashboardVO(
                    siteExportLimitW,
                    "5.0.0",
                    lastStatusDateUtc,
                    acLoadStatus._1,
                    backupLoadStatus._2,
                    backupLoadStatus._1,
                    grid._2,
                    grid._1,
                    inverterStatus._1,
                    inverterStatus._2,
                    inverterStatus._3,
                    inverterStatus._4,
                    thirdPartyInverterStatus._1,
                    pvStatus._1,
                    batteryStatus,
                    ouijaBoard._1,
                    ouijaBoard._2,
                    ouijaBoard._3,
                    bmsVersionMismatch,
                    dayTotalsE,
                    null
            );

            return new AllAboutSiteOverview(
                    publicSiteId,
                    siteData,
                    siteDevices,
                    coordinator.getTodaysDateInLocalTime(),
                    hasSupportForConnectedPV,
                    maximumPossiblePVOutput,
                    hasSolar,
                    hasBatteries,
                    batteryMismatchProtectionEnabled,
                    supportsLoadContributorsSince,
                    measuringThirdPartyInverter,
                    isAcCoupledMode
            );
        } catch (Exception ex) {
            log.error("Error processing site overview for site {}", publicSiteId, ex);
            return null;
        }
    }

    @Override
    public List<DailyTotalVO> processSiteNDayHistory(String publicSiteId, List<Tuple2<AllAboutDevice, List<DailyTotalVO>>> deviceHistories, int twiceDays) {
        // 提取所有设备并找到协调器
        List<AllAboutDevice> siteDevices = deviceHistories.stream()
                .map(e -> e._1)
                .collect(Collectors.toList());

        Tuple2<AllAboutDevice, List<DailyTotalVO>> coordinator = deviceHistories.stream()
                .filter(h -> h._1.getPhaseRole() == PhaseRole.Coordinator)
                .findFirst()
                .orElse(null);

        // 按日期分组所有设备的总计数据
        Map<Timestamp, List<DailyTotalVO>> dailyTotals = new HashMap<>();
        if (deviceHistories != null) {
            for (Tuple2<AllAboutDevice, List<DailyTotalVO>> dailyHistory : deviceHistories) {
                if (dailyHistory._2 != null) {
                    for (DailyTotalVO dailyTotalVO : dailyHistory._2) {
                        if (dailyTotalVO != null && dailyTotalVO.getDateDate() != null) {
                            if (!dailyTotals.containsKey(dailyTotalVO.getDateDate())) {
                                dailyTotals.put(dailyTotalVO.getDateDate(), new ArrayList<>());
                            }

                            dailyTotals.get(dailyTotalVO.getDateDate()).add(dailyTotalVO);
                        }
                    }
                }
            }
        }

        // 按日期降序处理数据
        return dailyTotals.entrySet().stream()
                .sorted(Map.Entry.<Timestamp, List<DailyTotalVO>>comparingByKey().reversed())
                .map(entry -> {
                    List<DailyTotalVO> dayTotals = entry.getValue();
                    Timestamp currentDate = entry.getKey();

                    // 获取协调器当天的数据
                    DailyTotalVO coordinatorDayTotal = coordinator._2
                            .stream()
                            .filter(t -> t.getDateDate().equals(currentDate))
                            .findFirst()
                            .orElse(null);

                    // 提取协调器的导入/导出数据
                    int dailyExportWh = coordinatorDayTotal != null && coordinatorDayTotal.getDailySoldWh() != null ? coordinatorDayTotal.getDailySoldWh() : 0;
                    boolean exportAdjusted = coordinatorDayTotal != null && coordinatorDayTotal.getDailySoldAdjusted();

                    int dailyImportWh = coordinatorDayTotal != null && coordinatorDayTotal.getDailyBoughtWh() != null ? coordinatorDayTotal.getDailyBoughtWh() : 0;
                    boolean importAdjusted = coordinatorDayTotal != null && coordinatorDayTotal.getDailyBoughtAdjusted();

                    // 计算其他指标
                    Integer dailyGenerationWh = dayTotals.stream()
                            .map(DailyTotalVO::getDailyGenerationWh)
                            .filter(Objects::nonNull)
                            .reduce(Integer::sum).orElse(null);
                    boolean generationAdjusted = dayTotals.stream()
                            .anyMatch(DailyTotalVO::getDailyGenerationAdjusted);

                    Integer dailyBatteryChargedWh = dayTotals.stream()
                            .map(DailyTotalVO::getDailyBatteryChargedWh)
                            .filter(Objects::nonNull)
                            .reduce(Integer::sum).orElse(null);
                    boolean batteryChargedAdjusted = dayTotals.stream()
                            .anyMatch(t -> Boolean.TRUE.equals(t.getDailyBatteryChargedAdjusted()));

                    Integer dailyBatteryDischargedWh = dayTotals.stream()
                            .map(DailyTotalVO::getDailyBatteryDischargedWh)
                            .filter(Objects::nonNull)
                            .reduce(Integer::sum).orElse(null);
                    boolean batteryDischargedAdjusted = dayTotals.stream()
                            .anyMatch(t -> Boolean.TRUE.equals(t.getDailyBatteryDischargedAdjusted()));

                    // 计算电池消耗和使用量
                    int dailyBatteryDrainWh = (dailyBatteryDischargedWh != null ? dailyBatteryDischargedWh : 0) -
                            (dailyBatteryChargedWh != null ? dailyBatteryChargedWh : 0);
                    int dailyUsageWh = (dailyImportWh - dailyExportWh) + dailyGenerationWh + dailyBatteryDrainWh;

                    // 构建结果DTO
                    DailyTotalVO dailyTotalVO = new DailyTotalVO();
                    dailyTotalVO.setDate(dayTotals.get(0).getDate());
                    dailyTotalVO.setDateDate(currentDate);
                    dailyTotalVO.setDailyUsageWh(dailyUsageWh);
                    dailyTotalVO.setDailySoldWh(dailyExportWh);
                    dailyTotalVO.setDailyBoughtWh(dailyImportWh);
                    dailyTotalVO.setDailyGenerationWh(dailyGenerationWh);
                    dailyTotalVO.setDailyBatteryChargedWh(dailyBatteryChargedWh);
                    dailyTotalVO.setDailyBatteryDischargedWh(dailyBatteryDischargedWh);
                    dailyTotalVO.setDailyUsageAdjusted(usageAdjusted(importAdjusted, exportAdjusted, generationAdjusted,
                            batteryChargedAdjusted, batteryDischargedAdjusted));
                    dailyTotalVO.setDailySoldAdjusted(exportAdjusted);
                    dailyTotalVO.setDailyBoughtAdjusted(importAdjusted);
                    dailyTotalVO.setDailyGenerationAdjusted(generationAdjusted);
                    dailyTotalVO.setDailyBatteryChargedAdjusted(batteryChargedAdjusted);
                    dailyTotalVO.setDailyBatteryDischargedAdjusted(batteryDischargedAdjusted);
                    return dailyTotalVO;
                })
                .collect(Collectors.toList());
    }

    @Override
    public BatteryStatusDto processSiteBatteryStatus(AllAboutSiteOverview allAboutSiteOverview, EnergyFlowDto siteFlow, int numberOfConsecutiveDaysBatteriesMismatchedToDisabled) {
        return SiteOverviewHelper.processSiteBatteryStatus(
                allAboutSiteOverview,
                siteFlow,
                numberOfConsecutiveDaysBatteriesMismatchedToDisabled);
    }

    @Override
    public BigDecimal processSiteRenewablePercent(String publicSiteId, List<ProductWithDailyCache> productsWithDailies) {
        return Optional.ofNullable(SiteOverviewHelper.getRenewablePercentageOnSamePhase(publicSiteId, productsWithDailies))
                .map(BigDecimal::valueOf).orElse(null);
    }

    // 辅助方法：判断使用量是否调整
    private static boolean usageAdjusted(boolean importAdj, boolean exportAdj, boolean genAdj,
                                         boolean battChargeAdj, boolean battDischargeAdj) {
        return importAdj || exportAdj || genAdj || battChargeAdj || battDischargeAdj;
    }

}
